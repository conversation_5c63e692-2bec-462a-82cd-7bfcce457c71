import {
  CalendarDaysIcon,
  CameraIcon,
  ChartPieIcon,
  CurrencyDollarIcon,
  UserCircleIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
} from "@heroicons/vue/24/solid";
import { defineStore } from "pinia";
import { useLoginStore } from "~/store/user";

export type Routes = {
  to: string;
  label: string;
  icon: typeof CurrencyDollarIcon | string;
};
// Create a reactive function for routes
function getRoutes() {
  // Get user info from the login store
  const loginStore = useLoginStore();
  const isSuperAdmin = loginStore.isSuperAdmin || (process.client && localStorage.getItem('super_admin_token'));
  const hasAnalyticsAccess = loginStore.hasAnalyticsAccess;

  const routes: Routes[] = [];

  if (isSuperAdmin) {
    // Super admin routes
    routes.push(
      {
        to: "/analytics",
        label: "Analytics",
        icon: ChartBarIcon,
      },
      {
        to: "/profissionais",
        label: "Profissionais",
        icon: UserCircleIcon,
      },
      {
        to: "/pacientes",
        label: "Pacientes",
        icon: UserCircleIcon,
      },
      {
        to: "/solicitacoes",
        label: "Solicitações",
        icon: CurrencyDollarIcon,
      }
    );
  } else {
    // Regular professional routes
    routes.push(
      {
        to: "/atendimento",
        label: "Atendimento",
        icon: WrenchScrewdriverIcon,
      },
      {
        to: "/agenda",
        label: "Agenda",
        icon: CalendarDaysIcon,
      },
      {
        to: "/historico",
        label: "Histórico",
        icon: CurrencyDollarIcon,
      },
      {
        to: "/feed",
        label: "Feed",
        icon: CameraIcon,
      },
      {
        to: "/dashboard",
        label: "Faturamento",
        icon: ChartPieIcon,
      },
      {
        to: "/perfil",
        label: "Perfil",
        icon: UserCircleIcon,
      }
    );

    // Add analytics for professionals who have access
    if (hasAnalyticsAccess) {
      routes.push({
        to: "/analytics",
        label: "Analytics",
        icon: ChartBarIcon,
      });
    }
  }

  return routes;
}

export const useNavigationBottom = defineStore("navigationBottom", {
  state: () => ({
    active: false,
    refreshKey: 0,
  }),
  getters: {
    routes: () => getRoutes(),
  },
  actions: {
    toggleActive() {
      this.active = !this.active;
    },
    forceRefresh() {
      this.refreshKey++;
    },
  },
  persist: true,
});
