<template>
  <div class="w-full">
    <div class="container-table pt-1 lg:h-sm">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Analytics Dashboard</h1>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <NuxtLink to="/analytics/overview" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-blue-600 text-2xl mr-3">📊</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Visão Geral</p>
                <p class="text-lg font-bold text-gray-800">Overview</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/receita" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-green-600 text-2xl mr-3">💰</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Receita</p>
                <p class="text-lg font-bold text-gray-800">Revenue</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/usuarios" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-purple-600 text-2xl mr-3">👥</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Usuários</p>
                <p class="text-lg font-bold text-gray-800">Users</p>
              </div>
            </div>
          </NuxtLink>

          <NuxtLink to="/analytics/overview" class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center">
              <div class="text-orange-600 text-2xl mr-3">📈</div>
              <div>
                <p class="text-sm text-gray-600 font-medium">Relatórios</p>
                <p class="text-lg font-bold text-gray-800">Reports</p>
              </div>
            </div>
          </NuxtLink>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h2 class="text-lg font-semibold mb-4">Analytics Dashboard</h2>
          <p class="text-gray-600 mb-4">Bem-vindo ao painel de analytics! Escolha uma das opções acima para visualizar dados específicos.</p>
          
          <div class="mt-4 text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
            📊 Dashboard de Analytics funcionando corretamente!
            <p class="text-sm mt-2">Clique nos cards acima para navegar para diferentes seções.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'professionals'
});
</script>
