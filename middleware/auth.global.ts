import { useLoginStore } from "~/store/user";

export default defineNuxtRouteMiddleware((to) => {
  // Check if we're on the client side
  if (process.client) {
    const loginStore = useLoginStore();

    // Check both store state and localStorage for authentication
    // Pinia persistence stores the entire store state under the store name
    let persistedData = null;
    try {
      const persistedState = localStorage.getItem('login');
      persistedData = persistedState ? JSON.parse(persistedState) : null;
    } catch (e) {
      // Ignore JSON parse errors
    }

    const hasToken = loginStore.token || persistedData?.token;
    const hasSuperAdminToken = localStorage.getItem('super_admin_token');
    const isLoggedIn = !!(hasToken || hasSuperAdminToken);

    // If we have a token but store is not hydrated, hydrate it
    if ((hasToken || hasSuperAdminToken) && !loginStore.isLoggedIn) {
      if (hasSuperAdminToken) {
        loginStore.login(hasSuperAdminToken, true);
      } else if (hasToken) {
        loginStore.login(hasToken, false);
      }

      // If we have persisted user data, restore it
      if (persistedData?.userInfo) {
        loginStore.setUser(persistedData.userInfo);
      }
      if (persistedData?.isSuperAdmin) {
        loginStore.setSuperAdmin(persistedData.isSuperAdmin);
      }
    }

    const guestRoutes = [
      "/login",
      "/criar-conta",
      "/forgot-password",
      "/recover-password",
      "/completar-cadastro",
      "/super-admin-login",
    ];

    // Handle root path early
    if (to.path === "/") {
      if (isLoggedIn) return; // Allow logged-in users
      return navigateTo("/login"); // Redirect others
    }

    // Remove trailing slash EXCEPT for root
    if (to.path.endsWith("/") && to.path !== "/") {
      const newPath = to.path.replace(/\/+$/, ""); // Trim all trailing slashes
      return navigateTo(newPath, { redirectCode: 301 });
    }

    // Auth checks
    if (!isLoggedIn && !guestRoutes.includes(to.path)) {
      return navigateTo("/login");
    }

    if (isLoggedIn && guestRoutes.includes(to.path)) {
      return navigateTo("/analytics");
    }
  } else {
    // Server-side: be more permissive to avoid hydration mismatches
    // Only handle trailing slashes on server
    if (to.path.endsWith("/") && to.path !== "/") {
      const newPath = to.path.replace(/\/+$/, "");
      return navigateTo(newPath, { redirectCode: 301 });
    }
  }
});
