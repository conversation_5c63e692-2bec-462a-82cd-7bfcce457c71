import { useLoginStore } from "~/store/user";

export default defineNuxtRouteMiddleware((to) => {
  // Only apply to analytics routes
  if (!to.path.startsWith('/analytics')) {
    return;
  }

  // Only run on client side to avoid hydration issues
  if (process.client) {
    const loginStore = useLoginStore();

    // Check both store state and localStorage for authentication
    let persistedData = null;
    try {
      const persistedState = localStorage.getItem('login');
      persistedData = persistedState ? JSON.parse(persistedState) : null;
    } catch (e) {
      // Ignore JSON parse errors
    }

    const hasToken = loginStore.token || persistedData?.token;
    const hasSuperAdminToken = localStorage.getItem('super_admin_token');
    const isLoggedIn = !!(hasToken || hasSuperAdminToken);

    // Check if user is logged in
    if (!isLoggedIn) {
      return navigateTo("/login");
    }

    // For now, allow all logged-in users to access analytics
    // TODO: Re-enable permission check when backend is ready
    // if (!loginStore.hasAnalyticsAccess) {
    //   return navigateTo("/");
    // }
  }
});
