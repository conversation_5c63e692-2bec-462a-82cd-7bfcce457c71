import { useLoginStore } from "~/store/user";

export default defineNuxtRouteMiddleware((to) => {
  // Only apply to analytics routes
  if (!to.path.startsWith('/analytics')) {
    return;
  }

  const loginStore = useLoginStore();

  // Check if user is logged in
  if (!loginStore.isLoggedIn) {
    return navigateTo("/login");
  }

  // For now, allow all logged-in users to access analytics
  // TODO: Re-enable permission check when backend is ready
  // if (!loginStore.hasAnalyticsAccess) {
  //   return navigateTo("/");
  // }
});
