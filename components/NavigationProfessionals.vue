<template>
  <div class="btm-nav flex" :key="navigationBottom.refreshKey">
    <button
      v-for="({ icon, to, label }, i) in routes"
      :key="`${navigationBottom.refreshKey}-${i}`"
      :class="to === route.path ? 'active-navigation' : 'bg-base-200'"
      class="relative px-1"
      @click="router.push(to)"
    >
      <component
        :class="to === route.path ? 'fill-primary-content' : ''"
        :is="icon"
        class="w-5 h-5"
      ></component>
      <span class="text-xs">{{ label }}</span>
    </button>
  </div>
</template>
<script setup lang="ts">
import { useNavigationBottom } from "@/store/navigationBottom";
import { useLoginStore } from "@/store/user";

const navigationBottom = useNavigationBottom();
const loginStore = useLoginStore();
const router = useRouter();
const route = useRoute();

// Make routes reactive to login state changes
const routes = computed(() => {
  // Force reactivity by watching login state
  const isLoggedIn = loginStore.isLoggedIn;
  const isSuperAdmin = loginStore.isSuperAdmin;
  const hasAnalyticsAccess = loginStore.hasAnalyticsAccess;

  return navigationBottom.routes;
});

// Watch for login state changes and force refresh
watch(() => loginStore.isLoggedIn, () => {
  navigationBottom.forceRefresh();
});

watch(() => loginStore.isSuperAdmin, () => {
  navigationBottom.forceRefresh();
});
</script>
<style lang="scss">
.active-navigation {
  @apply active bg-primary border-primary-content text-primary-content;
}
</style>
