export default defineNuxtPlugin(() => {
  // This plugin runs on client-side only to ensure proper auth state hydration
  const loginStore = useLoginStore();
  
  // Check if we have persisted auth data but store is not hydrated
  if (process.client) {
    try {
      const persistedState = localStorage.getItem('login');
      const persistedData = persistedState ? JSON.parse(persistedState) : null;
      const hasSuperAdminToken = localStorage.getItem('super_admin_token');
      
      // If we have tokens but store is not properly hydrated, restore the state
      if ((persistedData?.token || hasSuperAdminToken) && !loginStore.isLoggedIn) {
        if (hasSuperAdminToken) {
          loginStore.login(hasSuperAdminToken, true);
        } else if (persistedData?.token) {
          loginStore.login(persistedData.token, persistedData.isSuperAdmin || false);
        }
        
        // Restore user data
        if (persistedData?.userInfo) {
          loginStore.setUser(persistedData.userInfo);
        }
        
        // Restore super admin status
        if (persistedData?.isSuperAdmin) {
          loginStore.setSuperAdmin(persistedData.isSuperAdmin);
        }
      }
    } catch (error) {
      console.warn('Failed to hydrate auth state:', error);
    }
  }
});
